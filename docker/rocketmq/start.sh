#!/bin/bash

# RocketMQ Docker 启动脚本
# 用于快速启动本地RocketMQ开发环境

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 检查Docker是否运行
check_docker() {
    print_message $BLUE "检查Docker状态..."
    if ! docker info > /dev/null 2>&1; then
        print_message $RED "错误: Docker未运行，请先启动Docker"
        exit 1
    fi
    print_message $GREEN "Docker运行正常"
}

# 创建必要的目录
create_directories() {
    print_message $BLUE "创建必要的目录结构..."
    
    # 创建数据目录
    mkdir -p data/nameserver/{logs,store}
    mkdir -p data/broker/{logs,store}
    mkdir -p data/redis
    mkdir -p data/mysql
    mkdir -p init
    
    # 设置权限
    chmod -R 755 data/
    
    print_message $GREEN "目录创建完成"
}

# 启动服务
start_services() {
    print_message $BLUE "启动RocketMQ服务..."
    
    # 启动所有服务
    docker-compose up -d
    
    print_message $GREEN "服务启动命令已执行"
}

# 等待服务启动
wait_for_services() {
    print_message $BLUE "等待服务启动..."
    
    # 等待NameServer启动
    print_message $YELLOW "等待NameServer启动..."
    for i in {1..30}; do
        if docker logs rocketmq-nameserver 2>&1 | grep -q "The Name Server boot success"; then
            print_message $GREEN "NameServer启动成功"
            break
        fi
        if [ $i -eq 30 ]; then
            print_message $RED "NameServer启动超时"
            exit 1
        fi
        sleep 2
    done
    
    # 等待Broker启动
    print_message $YELLOW "等待Broker启动..."
    for i in {1..30}; do
        if docker logs rocketmq-broker 2>&1 | grep -q "The broker.*boot success"; then
            print_message $GREEN "Broker启动成功"
            break
        fi
        if [ $i -eq 30 ]; then
            print_message $RED "Broker启动超时"
            exit 1
        fi
        sleep 2
    done
    
    # 等待Console启动
    print_message $YELLOW "等待Console启动..."
    for i in {1..30}; do
        if curl -s http://localhost:8080 > /dev/null 2>&1; then
            print_message $GREEN "Console启动成功"
            break
        fi
        if [ $i -eq 30 ]; then
            print_message $YELLOW "Console启动可能需要更多时间，请稍后手动检查"
            break
        fi
        sleep 2
    done
}

# 创建Topic
create_topics() {
    print_message $BLUE "创建必要的Topic..."
    
    # 等待一下确保Broker完全启动
    sleep 5
    
    # 创建简历解析Topic
    if docker exec rocketmq-broker sh mqadmin updateTopic -n localhost:9876 -t RESUME_PARSE_TOPIC -c DefaultCluster > /dev/null 2>&1; then
        print_message $GREEN "RESUME_PARSE_TOPIC 创建成功"
    else
        print_message $YELLOW "RESUME_PARSE_TOPIC 创建失败，可能已存在"
    fi
    
    # 创建文件上传Topic
    if docker exec rocketmq-broker sh mqadmin updateTopic -n localhost:9876 -t FILE_UPLOAD_TOPIC -c DefaultCluster > /dev/null 2>&1; then
        print_message $GREEN "FILE_UPLOAD_TOPIC 创建成功"
    else
        print_message $YELLOW "FILE_UPLOAD_TOPIC 创建失败，可能已存在"
    fi
    
    # 创建测试Topic
    if docker exec rocketmq-broker sh mqadmin updateTopic -n localhost:9876 -t TEST_TOPIC -c DefaultCluster > /dev/null 2>&1; then
        print_message $GREEN "TEST_TOPIC 创建成功"
    else
        print_message $YELLOW "TEST_TOPIC 创建失败，可能已存在"
    fi
}

# 显示服务状态
show_status() {
    print_message $BLUE "服务状态检查..."
    
    echo ""
    print_message $BLUE "=== Docker容器状态 ==="
    docker-compose ps
    
    echo ""
    print_message $BLUE "=== Topic列表 ==="
    docker exec rocketmq-broker sh mqadmin topicList -n localhost:9876 2>/dev/null || print_message $YELLOW "无法获取Topic列表"
    
    echo ""
    print_message $BLUE "=== 集群信息 ==="
    docker exec rocketmq-broker sh mqadmin clusterList -n localhost:9876 2>/dev/null || print_message $YELLOW "无法获取集群信息"
}

# 显示访问信息
show_access_info() {
    echo ""
    print_message $GREEN "=== RocketMQ 启动完成 ==="
    echo ""
    print_message $BLUE "服务访问信息:"
    echo "  NameServer:    localhost:9876"
    echo "  Broker:        localhost:10911"
    echo "  Console:       http://localhost:8080"
    echo "  Redis:         localhost:6379"
    echo "  MySQL:         localhost:3306"
    echo ""
    print_message $BLUE "应用配置:"
    echo "  Profile:       local"
    echo "  RocketMQ:      localhost:9876"
    echo ""
    print_message $BLUE "启动应用:"
    echo "  mvn spring-boot:run -Dspring-boot.run.profiles=local"
    echo ""
    print_message $BLUE "测试接口:"
    echo "  curl -X POST http://localhost:8080/test/mq/send-resume-parse"
    echo ""
    print_message $YELLOW "注意: 如果Console无法访问，请等待1-2分钟后重试"
}

# 主函数
main() {
    print_message $GREEN "=== RocketMQ Docker 环境启动脚本 ==="
    echo ""

    # 检查是否在正确的目录，如果不是则自动切换
    if [ ! -f "docker-compose.yml" ]; then
        # 检查是否在项目根目录
        if [ -f "docker/rocketmq/docker-compose.yml" ]; then
            print_message $YELLOW "自动切换到 docker/rocketmq 目录..."
            cd docker/rocketmq
        else
            print_message $RED "错误: 找不到docker-compose.yml文件"
            print_message $RED "请确保在项目根目录或docker/rocketmq目录中运行此脚本"
            exit 1
        fi
    fi
    
    check_docker
    create_directories
    start_services
    wait_for_services
    create_topics
    show_status
    show_access_info
    
    print_message $GREEN "RocketMQ环境启动完成！"
}

# 如果脚本被直接执行
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
