version: '3.8'

services:
  # RocketMQ NameServer
  rocketmq-nameserver:
    image: apache/rocketmq:5.1.4
    container_name: rocketmq-nameserver
    ports:
      - "9876:9876"
    volumes:
      - ./data/nameserver/logs:/opt/logs
      - ./data/nameserver/store:/opt/store
    environment:
      JAVA_OPT: "-Duser.home=/opt -Xms512M -Xmx512M -Xmn128m"
    command: ["sh", "mqnamesrv"]
    networks:
      - rocketmq-network
    restart: unless-stopped

  # RocketMQ Broker
  rocketmq-broker:
    image: apache/rocketmq:5.1.4
    container_name: rocketmq-broker
    ports:
      - "10909:10909"
      - "10911:10911"
    volumes:
      - ./data/broker/logs:/opt/logs
      - ./data/broker/store:/opt/store
      - ./conf/broker.conf:/opt/rocketmq-5.1.4/conf/broker.conf
    environment:
      NAMESRV_ADDR: "rocketmq-nameserver:9876"
      JAVA_OPT_EXT: "-Du<PERSON>.home=/opt -Xms512M -Xmx512M -Xmn128m"
      BROKER_IP1: rocketmq-broker
      BROKER_IP2: rocketmq-broker
    command: ["sh", "mqbroker", "-c", "/opt/rocketmq-5.1.4/conf/broker.conf"]
    depends_on:
      - rocketmq-nameserver
    networks:
      - rocketmq-network
    restart: unless-stopped

  # RocketMQ Console (管理界面)
  rocketmq-console:
    image: styletang/rocketmq-console-ng:latest
    container_name: rocketmq-console
    ports:
      - "8080:8080"
    environment:
      JAVA_OPTS: "-Drocketmq.namesrv.addr=rocketmq-nameserver:9876 -Dcom.rocketmq.sendMessageWithVIPChannel=false"
    depends_on:
      - rocketmq-nameserver
      - rocketmq-broker
    networks:
      - rocketmq-network
    restart: unless-stopped

networks:
  rocketmq-network:
    driver: bridge

volumes:
  nameserver-logs:
  nameserver-store:
  broker-logs:
  broker-store:
  redis-data:
  mysql-data:
