package com.tinyzk.user.center.mq;

import com.tinyzk.user.center.dto.ResumeParseMessage;
import com.tinyzk.user.center.service.MessageProducerService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * 消息消费者测试
 */
@SpringBootTest
@ActiveProfiles("local")
@Slf4j
public class MessageConsumerTest {

    @Autowired
    private MessageProducerService messageProducerService;

    @Test
    public void testResumeParseMessageConsume() throws Exception {
        log.info("=== 开始测试简历解析消息消费 ===");
        
        // 构造简历解析消息
        ResumeParseMessage message = new ResumeParseMessage();
        message.setMessageId("TEST_MSG_" + System.currentTimeMillis());
        message.setBatchId("TEST_BATCH_" + System.currentTimeMillis());
        message.setUserId(12345L);
        message.setFileName("test-resume.pdf");
        message.setFileSize(1024000L);
        message.setFileType("pdf");
        message.setOssKey("test/resumes/test-resume.pdf");
        message.setCreateTime(LocalDateTime.now());
        
        try {
            log.info("发送简历解析消息: {}", message);
            
            // 发送消息
            messageProducerService.sendResumeParseMessage(message);
            
            log.info("消息发送成功，等待消费...");
            
            // 等待消息被消费
            Thread.sleep(5000);
            
            log.info("=== 简历解析消息消费测试完成 ===");
            
        } catch (Exception e) {
            log.error("简历解析消息消费测试失败", e);
            throw e;
        }
    }

    @Test
    public void testSimpleMessageConsume() throws Exception {
        log.info("=== 开始测试简单消息消费 ===");
        
        // 构造一个最简单的消息
        ResumeParseMessage message = new ResumeParseMessage();
        message.setMessageId("SIMPLE_TEST_" + UUID.randomUUID().toString());
        message.setBatchId("SIMPLE_BATCH");
        message.setFileName("simple-test.txt");
        message.setFileSize(100L);
        message.setFileType("txt");
        message.setOssKey("test/simple.txt");
        message.setCreateTime(LocalDateTime.now());
        
        try {
            log.info("发送简单测试消息: messageId={}", message.getMessageId());
            
            // 发送消息
            messageProducerService.sendResumeParseMessage(message);
            
            log.info("简单消息发送成功，等待消费...");
            
            // 等待消息被消费
            Thread.sleep(3000);
            
            log.info("=== 简单消息消费测试完成 ===");
            
        } catch (Exception e) {
            log.error("简单消息消费测试失败", e);
            throw e;
        }
    }

    @Test
    public void testMultipleMessageConsume() throws Exception {
        log.info("=== 开始测试多条消息消费 ===");
        
        int messageCount = 3;
        
        for (int i = 0; i < messageCount; i++) {
            ResumeParseMessage message = new ResumeParseMessage();
            message.setMessageId("MULTI_TEST_" + i + "_" + System.currentTimeMillis());
            message.setBatchId("MULTI_BATCH");
            message.setFileName("multi-test-" + i + ".txt");
            message.setFileSize(100L + i);
            message.setFileType("txt");
            message.setOssKey("test/multi-" + i + ".txt");
            message.setCreateTime(LocalDateTime.now());
            
            try {
                log.info("发送第 {} 条消息: messageId={}", i + 1, message.getMessageId());
                
                // 发送消息
                messageProducerService.sendResumeParseMessage(message);
                
                // 短暂延迟
                Thread.sleep(500);
                
            } catch (Exception e) {
                log.error("发送第 {} 条消息失败", i + 1, e);
                throw e;
            }
        }
        
        log.info("所有消息发送完成，等待消费...");
        
        // 等待所有消息被消费
        Thread.sleep(8000);
        
        log.info("=== 多条消息消费测试完成 ===");
    }

    @Test
    public void testMessageConsumeWithLogs() throws Exception {
        log.info("=== 开始测试消息消费（观察日志） ===");
        
        // 构造测试消息
        ResumeParseMessage message = new ResumeParseMessage();
        message.setMessageId("LOG_TEST_" + System.currentTimeMillis());
        message.setBatchId("LOG_BATCH");
        message.setFileName("log-test.txt");
        message.setFileSize(200L);
        message.setFileType("txt");
        message.setOssKey("test/log-test.txt");
        message.setCreateTime(LocalDateTime.now());
        
        try {
            log.info("=== 发送消息前 ===");
            log.info("消息详情: {}", message);
            
            // 发送消息
            messageProducerService.sendResumeParseMessage(message);
            
            log.info("=== 消息发送后，开始等待消费 ===");
            
            // 分段等待，便于观察日志
            for (int i = 1; i <= 10; i++) {
                Thread.sleep(1000);
                log.info("等待消费中... {}秒", i);
            }
            
            log.info("=== 消息消费观察测试完成 ===");
            
        } catch (Exception e) {
            log.error("消息消费观察测试失败", e);
            throw e;
        }
    }
}
