## 服务配置
spring:
  application:
    name: user-center
  profiles:
    active: local
  aop:
    proxy-target-class: true
    
  main:
    allow-bean-definition-overriding: true
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB
  cloud:
    compatibility-verifier:
      enabled: false
    nacos:
      config:
        import-check:
          enabled: false
      discovery:
        enabled: false

management:
  server:
    port: 18080
  health:
    elasticsearch:
      enabled: false
  endpoints:
    web:
      base-path: /actuator
      exposure:
        include: '*'
  endpoint:
    health:
      show-details: always
    prometheus:
      enabled: true
  prometheus:
    metrics:
      export:
        enabled: true
  metrics:
    distribution:
      percentiles-histogram:
        "[http.server.requests]": true
      percentiles:
        "[http.server.requests]": 0.5, 0.95, 0.99
    tags:
      application: ${spring.application.name}
  tracing:
    sampling:
      probability: 0.1
  # SkyWalking 配置通过 Agent 参数设置，无需在配置文件中配置

# 缓存预热配置
cache:
  warmup:
    enabled: true
    startup-warmup: true
    schedule:
      enabled: true
      cron: "0 0 2 * * ?"
      before-expire: PT2M
    strategy:
      user-detail-count: 1000
      user-list-pages: 5
      page-size: 20
      hot-data-days: 7
      priorities:
        - userDetail
        - userList
        - clientDetails
        - userMapping
    resource:
      thread-pool-size: 5
      timeout: PT30M
      batch-size: 100
      interval: PT0.1S

# RocketMQ 配置
rocketmq:
  # 实例基础配置
  name-server: ${NAMESRV_ADDR:http://MQ_INST_xxxxxxxxxx.cn-hangzhou.mq.aliyuncs.com:80}
  access-key: ${ACCESS_KEY:your-access-key}
  secret-key: ${SECRET_KEY:your-secret-key}
  
  # 生产者配置
  producer:
    group: GID_PRODUCER_GROUP
    # 发送消息超时时间，单位毫秒
    send-message-timeout: 3000
    # 压缩消息体阈值，超过4K自动压缩
    compress-message-body-threshold: 4096
    # 最大消息大小，单位字节，默认4MB
    max-message-size: 4194304
    # 异步发送失败重试次数
    retry-times-when-send-async-failed: 2
    # 同步发送失败重试次数
    retry-times-when-send-failed: 2
    # 发送失败是否重试其他broker
    retry-next-server: true
    
  # 消费者配置
  consumer:
    # 消费者线程池配置
    consume-thread-min: 20
    consume-thread-max: 64
    # 单次拉取消息数量
    pull-batch-size: 32
    # 消费失败重试次数，-1表示16次
    max-reconsume-times: 16
    # 消费超时时间，单位分钟
    consume-timeout: 15
    # 消息消费失败后延迟级别
    delay-level-when-next-consume: 0
    # 是否顺序消费
    orderly: false
