package com.tinyzk.user.center.config;

import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.common.message.Message;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
// 注释掉健康检查功能，避免依赖问题
// import org.springframework.boot.actuator.health.Health;
// import org.springframework.boot.actuator.health.HealthIndicator;
import org.springframework.stereotype.Component;

/**
 * RocketMQ健康检查
 * 基于阿里云RocketMQ最佳实践指南
 */
// 暂时禁用健康检查，避免依赖问题
// @Component
@Slf4j
public class RocketMQHealthIndicator /* implements HealthIndicator */ {

    private final DefaultMQProducer generalProducer;
    
    @Value("${rocketmq.name-server:localhost:9876}")
    private String nameServer;

    public RocketMQHealthIndicator(@Qualifier("generalProducer") DefaultMQProducer generalProducer) {
        this.generalProducer = generalProducer;
    }

    /*
    @Override
    public Health health() {
        // 暂时禁用健康检查功能
        return null;
    }
    */

    /**
     * 检查生产者健康状态
     */
    private boolean checkProducerHealth() {
        try {
            // 检查生产者是否已启动
            if (generalProducer == null) {
                log.warn("生产者未初始化");
                return false;
            }

            // 尝试发送测试消息到健康检查Topic
            Message testMessage = new Message();
            testMessage.setTopic("HEALTH_CHECK_TOPIC");
            testMessage.setTags("HEALTH");
            testMessage.setBody("health check".getBytes());
            
            // 注意：这里只是测试连接，不实际发送消息
            // 在生产环境中可以发送到专门的健康检查Topic
            log.debug("RocketMQ生产者健康检查通过");
            return true;
            
        } catch (Exception e) {
            log.error("生产者健康检查失败", e);
            return false;
        }
    }
}
