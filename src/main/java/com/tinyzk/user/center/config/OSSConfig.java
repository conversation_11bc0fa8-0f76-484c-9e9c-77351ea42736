package com.tinyzk.user.center.config;

import com.aliyun.oss.ClientBuilderConfiguration;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 阿里云OSS配置
 */
@Configuration
@Slf4j
public class OSSConfig {

    @Value("${aliyun.oss.endpoint}")
    private String endpoint;

    @Value("${aliyun.oss.internal-endpoint:}")
    private String internalEndpoint;

    @Value("${aliyun.oss.access-key-id}")
    private String accessKeyId;

    @Value("${aliyun.oss.access-key-secret}")
    private String accessKeySecret;

    @Value("${aliyun.oss.bucket-name}")
    private String bucketName;

    @Value("${aliyun.oss.connection-timeout:3000}")
    private int connectionTimeout;

    @Value("${aliyun.oss.socket-timeout:30000}")
    private int socketTimeout;

    @Value("${aliyun.oss.max-connections:50}")
    private int maxConnections;

    @Value("${aliyun.oss.max-error-retry:3}")
    private int maxErrorRetry;

    @Value("${aliyun.oss.use-internal-endpoint:false}")
    private boolean useInternalEndpoint;

    public String getEndpoint() {
        return endpoint;
    }

    public String getInternalEndpoint() {
        return internalEndpoint;
    }

    public String getAccessKeyId() {
        return accessKeyId;
    }

    public String getAccessKeySecret() {
        return accessKeySecret;
    }

    public String getBucketName() {
        return bucketName;
    }

    public int getConnectionTimeout() {
        return connectionTimeout;
    }

    public int getSocketTimeout() {
        return socketTimeout;
    }

    public int getMaxConnections() {
        return maxConnections;
    }

    public int getMaxErrorRetry() {
        return maxErrorRetry;
    }

    public boolean isUseInternalEndpoint() {
        return useInternalEndpoint;
    }


    /**
     * 创建OSS客户端
     */
    @Bean
    public OSS ossClient() {
        // 优化的客户端配置
        ClientBuilderConfiguration config = new ClientBuilderConfiguration();
        config.setMaxConnections(maxConnections);
        config.setSocketTimeout(socketTimeout);
        config.setConnectionTimeout(connectionTimeout);
        config.setMaxErrorRetry(maxErrorRetry);

        // 启用CRC校验
        config.setCrcCheckEnabled(true);

        // 选择端点
        String actualEndpoint = useInternalEndpoint && internalEndpoint != null ? internalEndpoint : endpoint;

        return new OSSClientBuilder().build(actualEndpoint, accessKeyId, accessKeySecret, config);
    }
}
