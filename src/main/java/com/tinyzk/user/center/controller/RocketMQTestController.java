package com.tinyzk.user.center.controller;

import com.tinyzk.user.center.dto.ResumeParseMessage;
import com.tinyzk.user.center.service.MessageProducerService;
import com.tinyzk.user.center.service.RocketMQTopicService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * RocketMQ测试控制器
 * 用于验证消息发送和消费功能
 */
@RestController
@RequestMapping("/api/test/rocketmq")
@Tag(name = "RocketMQ测试", description = "RocketMQ消息队列测试接口")
@Slf4j
public class RocketMQTestController {

    @Autowired
    private MessageProducerService messageProducerService;
    
    @Autowired
    private RocketMQTopicService topicService;

    /**
     * 测试简历解析消息发送
     */
    @PostMapping("/test-resume-parse")
    @Operation(summary = "测试简历解析消息发送", description = "发送测试简历解析消息到RocketMQ")
    public Map<String, Object> testResumeParseMessage() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 创建测试消息
            ResumeParseMessage testMessage = createTestResumeParseMessage();
            
            log.info("发送测试简历解析消息: {}", testMessage.getMessageId());
            
            // 发送消息
            messageProducerService.sendResumeParseMessage(testMessage);
            
            result.put("success", true);
            result.put("message", "测试消息发送成功");
            result.put("messageId", testMessage.getMessageId());
            result.put("timestamp", LocalDateTime.now());
            
            log.info("测试消息发送成功: messageId={}", testMessage.getMessageId());
            
        } catch (Exception e) {
            log.error("测试消息发送失败", e);
            result.put("success", false);
            result.put("message", "测试消息发送失败: " + e.getMessage());
            result.put("error", e.getClass().getSimpleName());
        }
        
        return result;
    }

    /**
     * 测试通用消息发送
     */
    @PostMapping("/test-general-message")
    @Operation(summary = "测试通用消息发送", description = "发送测试通用消息到RocketMQ")
    public Map<String, Object> testGeneralMessage(@RequestParam(defaultValue = "TEST_TOPIC") String topic,
                                                  @RequestParam(defaultValue = "TEST") String tag) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            Map<String, Object> testData = new HashMap<>();
            testData.put("messageId", UUID.randomUUID().toString());
            testData.put("content", "这是一条测试消息");
            testData.put("timestamp", LocalDateTime.now());
            
            log.info("发送测试通用消息到Topic: {}, Tag: {}", topic, tag);
            
            // 发送消息
            messageProducerService.sendGeneralMessage(topic, tag, testData);
            
            result.put("success", true);
            result.put("message", "测试消息发送成功");
            result.put("topic", topic);
            result.put("tag", tag);
            result.put("data", testData);
            
        } catch (Exception e) {
            log.error("测试通用消息发送失败", e);
            result.put("success", false);
            result.put("message", "测试消息发送失败: " + e.getMessage());
            result.put("error", e.getClass().getSimpleName());
        }
        
        return result;
    }

    /**
     * 获取Topic状态
     */
    @GetMapping("/topic-status")
    @Operation(summary = "获取Topic状态", description = "检查RocketMQ Topic状态")
    public Map<String, Object> getTopicStatus() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            result.put("RESUME_PARSE_TOPIC", topicService.getTopicStatus("RESUME_PARSE_TOPIC"));
            result.put("FILE_UPLOAD_TOPIC", topicService.getTopicStatus("FILE_UPLOAD_TOPIC"));
            result.put("HEALTH_CHECK_TOPIC", topicService.getTopicStatus("HEALTH_CHECK_TOPIC"));
            result.put("timestamp", LocalDateTime.now());
            
        } catch (Exception e) {
            log.error("获取Topic状态失败", e);
            result.put("error", e.getMessage());
        }
        
        return result;
    }

    /**
     * 获取Topic创建脚本
     */
    @GetMapping("/topic-creation-script")
    @Operation(summary = "获取Topic创建脚本", description = "获取创建所需Topic的Shell脚本")
    public Map<String, Object> getTopicCreationScript() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            String script = topicService.getTopicCreationScript();
            result.put("script", script);
            result.put("message", "请复制脚本并在服务器上执行");
            
        } catch (Exception e) {
            log.error("获取Topic创建脚本失败", e);
            result.put("error", e.getMessage());
        }
        
        return result;
    }

    /**
     * RocketMQ连接测试
     */
    @GetMapping("/connection-test")
    @Operation(summary = "RocketMQ连接测试", description = "测试RocketMQ连接状态")
    public Map<String, Object> connectionTest() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 这里可以添加更详细的连接测试逻辑
            result.put("nameServer", "localhost:9876");
            result.put("status", "连接测试需要通过健康检查端点进行");
            result.put("healthCheckUrl", "/actuator/health");
            result.put("timestamp", LocalDateTime.now());
            
        } catch (Exception e) {
            log.error("连接测试失败", e);
            result.put("error", e.getMessage());
        }
        
        return result;
    }

    /**
     * 创建测试简历解析消息
     */
    private ResumeParseMessage createTestResumeParseMessage() {
        ResumeParseMessage message = new ResumeParseMessage();
        message.setMessageId(UUID.randomUUID().toString());
        message.setBatchId("TEST_BATCH_" + System.currentTimeMillis());
        message.setFileName("test_resume.pdf");
        message.setFileType("pdf");
        message.setFileSize(1024L);
        message.setOssKey("test/resume/test_resume.pdf");
        message.setFileUrl("https://example.com/test_resume.pdf");
        message.setPriority(5);
        message.setCreateTime(LocalDateTime.now());
        message.setUserId(1L);
        message.setTenantId("test_tenant");
        
        // 设置解析参数
        ResumeParseMessage.ParseParams parseParams = new ResumeParseMessage.ParseParams();
        parseParams.setRawtext(true);
        parseParams.setHandleImage(true);
        parseParams.setAvatar(true);
        parseParams.setParseMode("fast");
        parseParams.setOcrMode("accurate");
        parseParams.setOcrService("OCR");
        message.setParseParams(parseParams);
        
        return message;
    }
}
