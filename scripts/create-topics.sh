#!/bin/bash

# RocketMQ Topic创建脚本
# 基于阿里云RocketMQ最佳实践指南

echo "=== RocketMQ Topic创建脚本 ==="
echo "基于阿里云RocketMQ最佳实践指南"
echo ""

# 检查Docker容器状态
echo "1. 检查RocketMQ容器状态..."
if ! docker ps | grep -q "rocketmq-broker"; then
    echo "❌ RocketMQ Broker容器未运行，请先启动RocketMQ服务"
    echo "启动命令: cd docker/rocketmq && docker-compose up -d"
    exit 1
fi

if ! docker ps | grep -q "rocketmq-nameserver"; then
    echo "❌ RocketMQ NameServer容器未运行，请先启动RocketMQ服务"
    exit 1
fi

echo "✅ RocketMQ容器运行正常"
echo ""

# 等待服务完全启动
echo "2. 等待RocketMQ服务完全启动..."
sleep 5

# 创建Topic列表
TOPICS=(
    "RESUME_PARSE_TOPIC"
    "FILE_UPLOAD_TOPIC" 
    "HEALTH_CHECK_TOPIC"
    "TEST_TOPIC"
)

echo "3. 开始创建Topic..."
echo ""

# 创建每个Topic
for topic in "${TOPICS[@]}"; do
    echo "创建Topic: $topic"
    
    # 执行Topic创建命令
    docker exec rocketmq-broker sh mqadmin updateTopic \
        -n localhost:9876 \
        -t "$topic" \
        -c DefaultCluster \
        -r 4 \
        -w 4
    
    if [ $? -eq 0 ]; then
        echo "✅ Topic创建成功: $topic"
    else
        echo "❌ Topic创建失败: $topic"
    fi
    
    echo ""
    sleep 1
done

echo "4. 验证Topic创建结果..."
echo ""

# 列出所有Topic
echo "当前所有Topic列表:"
docker exec rocketmq-broker sh mqadmin topicList -n localhost:9876

echo ""
echo "=== Topic创建完成 ==="
echo ""
echo "📋 后续步骤:"
echo "1. 访问RocketMQ Console: http://localhost:8080"
echo "2. 检查Topic状态和队列信息"
echo "3. 启动应用程序测试消息发送和消费"
echo "4. 使用测试接口验证: /api/test/rocketmq/test-resume-parse"
echo ""
echo "🔍 故障排查:"
echo "- 如果Topic创建失败，请检查RocketMQ服务状态"
echo "- 确保端口9876和10911没有被占用"
echo "- 检查Docker容器日志: docker logs rocketmq-broker"
echo ""
